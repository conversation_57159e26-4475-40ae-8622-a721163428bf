# CastMate Application Index

## 📋 Table of Contents
1. [Application Overview](#application-overview)
2. [Core Features](#core-features)
3. [Technology Stack](#technology-stack)
4. [Architecture](#architecture)
5. [API Endpoints](#api-endpoints)
6. [Database Schema](#database-schema)
7. [Key Components](#key-components)
8. [AI Integrations](#ai-integrations)
9. [File Structure](#file-structure)
10. [Configuration](#configuration)

## 🎭 Application Overview

**CastMate** is an AI-powered script rehearsal platform designed for actors to practice their lines with intelligent AI scene partners. The application combines voice synthesis, natural language processing, and document management to create an immersive rehearsal experience.

### Primary Use Cases
- Script upload and processing
- AI-powered scene partner conversations
- Voice-based rehearsals with ElevenLabs
- Self-tape recording and playback
- Line memorization assistance
- Script formatting and analysis

## 🚀 Core Features

### 1. Script Management
- **Upload Support**: PDF, DOCX, TXT files
- **Processing**: Automatic text extraction and formatting
- **Storage**: Firebase Storage with Firestore metadata
- **Vectorization**: Pinecone vector database for semantic search

### 2. AI Scene Partner
- **Voice Synthesis**: ElevenLabs integration for realistic character voices
- **Conversation**: Real-time chat with context-aware AI
- **Character Assignment**: Multiple voices for different characters
- **Emotional Adaptation**: AI responds with appropriate emotion and timing

### 3. Voice Features
- **Recording**: Browser-based audio recording
- **Transcription**: OpenAI Whisper for speech-to-text
- **Synthesis**: ElevenLabs for text-to-speech
- **Real-time Conversation**: Live voice chat with AI agents

### 4. User Experience
- **Authentication**: Google OAuth via NextAuth
- **Responsive Design**: Mobile and desktop optimized
- **Dark/Light Mode**: Theme switching
- **Progress Tracking**: Rehearsal history and analytics

## 🛠️ Technology Stack

### Frontend Technologies
- **Next.js 14+**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **DaisyUI**: Component library
- **Framer Motion**: Animations and transitions
- **React Hooks**: State management

### Backend Technologies
- **Next.js API Routes**: Server-side endpoints
- **Firebase Admin SDK**: Server-side Firebase operations
- **LangChain**: AI framework and document processing
- **Pinecone**: Vector database for embeddings
- **OpenAI**: GPT models and Whisper transcription

### AI & ML Services
- **ElevenLabs**: Voice synthesis and AI agents
- **OpenAI**: GPT models, embeddings, Whisper
- **Google AI**: Gemini models
- **Anthropic**: Claude models
- **Groq**: Fast inference

### Database & Storage
- **Firebase Firestore**: NoSQL document database
- **Firebase Storage**: File storage
- **Pinecone**: Vector database
- **Firebase Authentication**: User management

## 🏗️ Architecture

### High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   External      │
│   (Next.js)     │◄──►│   (API Routes)  │◄──►│   Services      │
│                 │    │                 │    │                 │
│ • React UI      │    │ • Authentication│    │ • ElevenLabs    │
│ • Voice Chat    │    │ • File Process  │    │ • OpenAI        │
│ • Script View   │    │ • Vector Search │    │ • Firebase      │
│ • Recording     │    │ • AI Integration│    │ • Pinecone      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Data Flow
1. **User Authentication**: Google OAuth → NextAuth → Firebase
2. **Script Upload**: File → Firebase Storage → Processing → Vectorization → Pinecone
3. **Chat Interaction**: User Message → Vector Search → AI Processing → Response
4. **Voice Rehearsal**: Voice Input → Transcription → AI Agent → Voice Output

## 🔌 API Endpoints

### Authentication
- `GET/POST /api/auth/[...nextauth]` - NextAuth handlers
- `GET /api/session` - Session management

### File Processing
- `POST /api/processScriptfile` - Process uploaded scripts
- `POST /api/extractPdfText` - Extract text from PDFs
- `POST /api/fileManager` - File management operations

### Chat & Messaging
- `POST /api/processMessage` - Handle chat messages
- `POST /api/chat` - Chat endpoint
- `POST /api/chatVector` - Vector-based chat
- `GET /api/searchChat` - Search chat history

### Voice & Audio
- `POST /api/transcribeAudio` - Audio transcription
- `POST /api/selfTakeAudio` - Audio recording
- `POST /api/selfTakeVideo` - Video recording

### Script Processing
- `POST /api/format-script` - Format scripts with AI
- `POST /api/format-script-fallback` - Fallback formatting
- `GET /api/get-formatted-script` - Retrieve formatted scripts

### ElevenLabs Integration
- `POST /api/elevenlabs-webhook` - ElevenLabs webhook handler
- `POST /api/test-voice-update` - Voice testing

### Utilities
- `POST /api/optimizeQuestion` - Question optimization
- `POST /api/questionnaire` - User feedback
- `POST /api/send-verification-email` - Email verification

## 🗄️ Database Schema

### Firestore Collections

#### Users Collection Structure
```
users/{userEmail}/
├── files/{fileId}                    # Script files metadata
│   ├── fileName: string
│   ├── fileType: string
│   ├── downloadURL: string
│   ├── uploadedAt: timestamp
│   └── processed: boolean
├── chats/{chatId}/                   # Chat sessions
│   ├── createdAt: timestamp
│   ├── fileDocumentId: string
│   └── messages/{messageId}/         # Individual messages
│       ├── text: string
│       ├── role: 'user' | 'ai'
│       ├── createdAt: timestamp
│       └── audioUrl?: string
├── formatted_scripts/{scriptId}/     # Formatted scripts
│   ├── originalFileName: string
│   ├── formattedContent: string
│   ├── createdAt: timestamp
│   └── status: string
├── ElevenLabsData/{docId}/          # ElevenLabs integration
│   ├── knowledgeBaseDocId: string
│   ├── knowledgeBaseId: string
│   ├── agentId: string
│   └── uploadedAt: timestamp
└── Accounts/{accountId}/            # User account details
    ├── name: string
    ├── email: string
    ├── createdAt: timestamp
    └── preferences: object
```

### Pinecone Index Structure
```
Index: {indexName}
├── Namespace: {fileDocumentId}
│   ├── Vector ID: {chunkId}
│   ├── Vector: [embedding values]
│   └── Metadata:
│       ├── chunk_id: string
│       ├── document_title: string
│       ├── page_number: number
│       ├── text: string
│       └── namespace: string
```

## 🧩 Key Components

### Core UI Components
- `app/page.tsx` - Main landing page
- `components/scriptreaderAI/Reader-modal.tsx` - Main script reader interface
- `components/ChatArea.tsx` - Chat interface
- `components/VoiceAssistants/VoiceComponent.tsx` - Voice chat component
- `components/DocViewer/DocumentReader.tsx` - Document viewer

### Processing Components
- `components/DocViewer/documentProcessors.ts` - File processing logic
- `lib/langchain.tsx` - LangChain integration
- `lib/pinecone.tsx` - Pinecone operations
- `components/scriptreaderAI/elevenlabs.ts` - ElevenLabs integration

### AI Agents
- `components/Agents/SupervisorAgent.ts` - Main AI supervisor
- `components/Agents/ProcessGroqMessagesAgent.ts` - Message processing
- `components/Agents/ChatHistoryAgent.ts` - Chat history management
- `components/Agents/VisualizationAgent.ts` - Data visualization

## 🤖 AI Integrations

### ElevenLabs Integration
- **Voice Synthesis**: Real-time text-to-speech
- **AI Agents**: Conversational AI with script context
- **Knowledge Base**: Automatic script indexing
- **Webhook Support**: Real-time updates

### OpenAI Integration
- **GPT Models**: Text generation and conversation
- **Whisper**: Audio transcription
- **Embeddings**: Vector generation for semantic search

### Google AI Integration
- **Gemini Models**: Advanced text processing
- **Script Formatting**: Intelligent script structure analysis

### LangChain Framework
- **Document Processing**: Text splitting and chunking
- **Vector Stores**: Pinecone integration
- **Retrieval**: Context-aware document search
- **Agents**: Multi-step AI workflows

## ⚙️ Configuration

### Environment Variables
See `GOOGLE_AI_SETUP.md` and `ELEVENLABS_TOOLS_SETUP.md` for detailed setup instructions.

### Key Configuration Files
- `next.config.mjs` - Next.js configuration
- `tsconfig.json` - TypeScript configuration
- `tailwind.config.ts` - Tailwind CSS configuration
- `firebase-security-rules-formatted-scripts.rules` - Firebase security rules

### Feature Flags & Settings
- Voice recording enabled by default
- ElevenLabs integration configurable
- Multiple LLM providers with fallbacks
- Analytics and logging configurable

## 📊 Analytics & Monitoring

### Analytics Collections
- `query_metrics` - Query performance tracking
- `token_metrics` - Token usage monitoring
- `chunk_metrics` - Vector search analytics
- `content_metrics` - Content processing metrics

### Logging System
- Structured logging with context
- Error tracking and recovery
- Performance monitoring
- User interaction analytics

## 🔐 Security & Authentication

### Authentication Flow
1. Google OAuth via NextAuth.js
2. Session management with JWT
3. Firebase Authentication integration
4. Role-based access control

### Security Features
- CSRF protection
- XSS prevention headers
- Content Security Policy
- Secure cookie handling
- API rate limiting

### Data Privacy
- User data isolation by email
- Encrypted file storage
- Secure API communications
- GDPR compliance considerations

## 🚀 Deployment & DevOps

### Build Process
- TypeScript compilation
- Next.js optimization
- Asset bundling and minification
- Environment variable validation

### Production Considerations
- Firebase hosting ready
- Vercel deployment optimized
- CDN integration for assets
- Database connection pooling

### Monitoring & Health Checks
- API endpoint monitoring
- Database connection health
- External service availability
- Error rate tracking

## 🧪 Testing Strategy

### Test Coverage Areas
- API endpoint functionality
- Authentication flows
- File processing pipelines
- AI integration responses
- Voice recording/playback

### Testing Tools
- Jest for unit testing
- Cypress for E2E testing
- Firebase emulators for local testing
- API testing with Postman/Insomnia

## 📈 Performance Optimization

### Frontend Optimizations
- Code splitting with Next.js
- Image optimization
- Lazy loading components
- Caching strategies

### Backend Optimizations
- Vector search optimization
- Database query optimization
- File processing efficiency
- API response caching

### AI Service Optimization
- Embedding caching
- Model response streaming
- Batch processing for uploads
- Connection pooling

## 🔧 Development Workflow

### Local Development
1. Clone repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Configure Firebase project
5. Set up Pinecone index
6. Configure AI service APIs
7. Run development server: `npm run dev`

### Code Organization
- Feature-based component organization
- Shared utilities in `/lib`
- Type definitions in `/types`
- API routes in `/app/api`
- Reusable hooks in `/hooks`

### Best Practices
- TypeScript strict mode enabled
- ESLint and Prettier configured
- Component prop validation
- Error boundary implementation
- Accessibility considerations

## 📚 Documentation

### Available Documentation
- `SCRIPT_READER_ANALYSIS.md` - Comprehensive technical analysis
- `GOOGLE_AI_SETUP.md` - Google AI integration guide
- `ELEVENLABS_RAG_INDEXING_IMPLEMENTATION.md` - ElevenLabs setup
- `VOICE_SELECTION_FIX_SUMMARY.md` - Voice feature documentation
- `MODALITY_SYNC_VERIFICATION_GUIDE.md` - Sync verification guide

### API Documentation
- Endpoint specifications
- Request/response schemas
- Authentication requirements
- Error handling patterns

## 🐛 Troubleshooting

### Common Issues
1. **Authentication Problems**: Check Google OAuth configuration
2. **File Upload Failures**: Verify Firebase Storage permissions
3. **Voice Features Not Working**: Confirm ElevenLabs API keys
4. **Vector Search Issues**: Check Pinecone index configuration
5. **AI Response Delays**: Monitor API rate limits

### Debug Tools
- Browser developer tools
- Firebase console
- Pinecone dashboard
- ElevenLabs console
- Next.js built-in debugging

## 🔄 Future Enhancements

### Planned Features
- Multi-language support
- Advanced script analysis
- Collaborative rehearsals
- Performance analytics dashboard
- Mobile app development

### Technical Improvements
- GraphQL API implementation
- Real-time collaboration features
- Advanced caching strategies
- Microservices architecture
- Enhanced AI model integration

---

*This index provides a comprehensive overview of the CastMate application. For specific implementation details, refer to the individual documentation files and source code.*
