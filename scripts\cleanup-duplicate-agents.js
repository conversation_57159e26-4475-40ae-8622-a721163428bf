/**
 * <PERSON><PERSON><PERSON> to help identify and clean up duplicate ElevenLabs agents
 * 
 * This script helps identify agents that were created with the old naming convention
 * and provides guidance on cleaning them up.
 * 
 * Usage: node scripts/cleanup-duplicate-agents.js
 */

const { ElevenLabsClient } = require("elevenlabs");

async function listAllAgents() {
  try {
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************';
    
    if (!apiKey) {
      console.error("❌ ElevenLabs API key not found. Please set NEXT_PUBLIC_ELEVENLABS_API_KEY");
      return;
    }

    console.log("🔍 Fetching all agents from ElevenLabs...");
    
    const client = new ElevenLabsClient({ apiKey });
    
    // Note: This assumes there's a method to list agents
    // You may need to check the ElevenLabs SDK documentation for the correct method
    try {
      const agents = await client.conversationalAi.getAgents();
      
      console.log(`\n📊 Found ${agents.length} total agents\n`);
      
      // Categorize agents
      const userAgents = [];
      const duplicateAgents = [];
      const otherAgents = [];
      
      agents.forEach(agent => {
        const name = agent.name || '';
        const agentId = agent.agent_id || '';
        
        // Check if it's a user-specific agent (new format)
        if (name.includes('@') && name.includes('-')) {
          userAgents.push(agent);
        }
        // Check if it's an old format duplicate
        else if (name.includes('CastMate Assistant for ') && name.includes('@')) {
          duplicateAgents.push(agent);
        }
        // Other agents
        else {
          otherAgents.push(agent);
        }
      });
      
      console.log("📋 Agent Categories:");
      console.log(`✅ User-specific agents (new format): ${userAgents.length}`);
      console.log(`⚠️  Duplicate agents (old format): ${duplicateAgents.length}`);
      console.log(`ℹ️  Other agents: ${otherAgents.length}`);
      
      if (userAgents.length > 0) {
        console.log("\n✅ User-specific agents (new format):");
        userAgents.forEach(agent => {
          console.log(`   - ${agent.name} (ID: ${agent.agent_id})`);
        });
      }
      
      if (duplicateAgents.length > 0) {
        console.log("\n⚠️  Duplicate agents (old format) - SHOULD BE DELETED:");
        duplicateAgents.forEach(agent => {
          console.log(`   - ${agent.name} (ID: ${agent.agent_id})`);
        });
        
        console.log("\n🧹 To clean up duplicates:");
        console.log("1. Go to https://elevenlabs.io/app/conversational-ai/agents");
        console.log("2. Delete agents with names like 'CastMate <NAME_EMAIL>'");
        console.log("3. Keep agents with names like '<EMAIL>-uuid'");
      }
      
      if (otherAgents.length > 0) {
        console.log("\nℹ️  Other agents:");
        otherAgents.forEach(agent => {
          console.log(`   - ${agent.name} (ID: ${agent.agent_id})`);
        });
      }
      
      // Check for potential duplicates by email
      const emailMap = new Map();
      [...userAgents, ...duplicateAgents].forEach(agent => {
        const email = extractEmailFromAgentName(agent.name);
        if (email) {
          if (!emailMap.has(email)) {
            emailMap.set(email, []);
          }
          emailMap.get(email).push(agent);
        }
      });
      
      console.log("\n🔍 Checking for users with multiple agents:");
      let foundDuplicates = false;
      emailMap.forEach((agents, email) => {
        if (agents.length > 1) {
          foundDuplicates = true;
          console.log(`\n⚠️  User ${email} has ${agents.length} agents:`);
          agents.forEach(agent => {
            const isNewFormat = agent.name.includes('@') && agent.name.includes('-') && !agent.name.includes('CastMate Assistant for');
            console.log(`   - ${agent.name} (ID: ${agent.agent_id}) ${isNewFormat ? '✅ KEEP' : '❌ DELETE'}`);
          });
        }
      });
      
      if (!foundDuplicates) {
        console.log("✅ No duplicate agents found for any user");
      }
      
    } catch (listError) {
      console.error("❌ Error listing agents:", listError.message);
      console.log("\n💡 Note: The ElevenLabs SDK might not have a getAgents() method.");
      console.log("Please check the ElevenLabs dashboard manually at:");
      console.log("https://elevenlabs.io/app/conversational-ai/agents");
    }
    
  } catch (error) {
    console.error("❌ Error:", error.message);
  }
}

function extractEmailFromAgentName(name) {
  // Extract email from both old and new formats
  if (name.includes('CastMate Assistant for ')) {
    return name.replace('CastMate Assistant for ', '');
  }
  
  // For new format, extract email part before the UUID
  const emailMatch = name.match(/^([^-]+@[^-]+)-[a-f0-9-]+$/);
  if (emailMatch) {
    return emailMatch[1];
  }
  
  return null;
}

// Run the script
if (require.main === module) {
  listAllAgents().catch(console.error);
}

module.exports = { listAllAgents, extractEmailFromAgentName };
