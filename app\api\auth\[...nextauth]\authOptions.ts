import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import { NextAuthOptions } from "next-auth";
import { ensureUserAgent } from "../../../../lib/userAgentManager";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    // You can add more providers here
  ],
  secret: process.env.NEXTAUTH_SECRET, // Ensure this is set in your .env file
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      try {
        // FIXED: Don't create agents during sign-in to avoid race conditions
        // Agent creation will happen on-demand when user actually uses voice features
        console.log(`[AUTH] User signed in: ${user.email}`);
        return true;
      } catch (error) {
        console.error('[AUTH] Error in signIn callback:', error);
        // Don't block sign-in due to any issues
        return true;
      }
    },
    async session({ session, token }) {
      // You can add agent-related data to the session here if needed
      return session;
    },
  },
};

// Export GET and POST handlers for NextAuth
const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };





// import NextAuth from "next-auth";
// import GoogleProvider from "next-auth/providers/google";
// import { NextAuthOptions } from "next-auth";

// export const authOptions: NextAuthOptions = {
//   providers: [
//     GoogleProvider({
//       clientId: process.env.GOOGLE_CLIENT_ID!,
//       clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
//     }),
//   ],
//   secret: process.env.NEXTAUTH_SECRET!,
//   pages: {
//     signIn: '/auth/signin',
//     error: '/auth/error',
//   },
// };

// const handler = NextAuth(authOptions);

// export { handler as GET, handler as POST };
