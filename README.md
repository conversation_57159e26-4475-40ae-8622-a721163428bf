# CastMate - AI-Powered Script Rehearsal Platform

CastMate is an intelligent script reading assistant application that utilizes AI to help actors practice their lines and rehearse scenes. The application provides both text-based chat functionality and voice-based rehearsal capabilities, creating an interactive AI scene partner for actors.

## 🎭 Application Overview

**Purpose**: Never rehearse alone again - CastMate transforms script rehearsals with AI-powered scene partners.

**Key Features**:
- AI Scene Partner with emotional and timing adaptation
- Voice synthesis with diverse character voices
- Self-tape recording capabilities
- Line memorization helpers
- Smart auto-scroll functionality
- Script upload and processing
- Real-time voice conversation with AI agents

## 🏗️ Architecture & Technology Stack

### Frontend
- **Framework**: Next.js 14+ with App Router
- **UI**: React 18+ with TypeScript
- **Styling**: Tailwind CSS with DaisyUI
- **Animations**: Framer Motion
- **State Management**: React Context + Zustand
- **Authentication**: NextAuth.js with Google OAuth

### Backend & APIs
- **Runtime**: Node.js with Next.js API Routes
- **Database**: Firebase Firestore
- **File Storage**: Firebase Storage
- **Vector Database**: Pinecone
- **Authentication**: NextAuth.js

### AI & ML Integrations
- **Voice Synthesis**: ElevenLabs API
- **LLM Models**:
  - Google AI (Gemini 2.5 Pro)
  - OpenAI GPT models
  - Anthropic Claude
  - Groq
- **Embeddings**: OpenAI Embeddings
- **Voice Transcription**: OpenAI Whisper
- **Framework**: LangChain

## 📁 Project Structure

```
├── app/                          # Next.js App Router
│   ├── api/                      # API Routes
│   │   ├── auth/                 # NextAuth configuration
│   │   ├── processScriptfile/    # Script processing
│   │   ├── processMessage/       # Chat message handling
│   │   ├── transcribeAudio/      # Voice transcription
│   │   ├── elevenlabs-webhook/   # ElevenLabs integration
│   │   └── [other APIs]/
│   ├── page.tsx                  # Main landing page
│   ├── layout.tsx                # Root layout
│   └── providers.tsx             # Context providers
├── components/                   # React components
│   ├── scriptreaderAI/          # Core script reader components
│   ├── VoiceAssistants/         # Voice interaction components
│   ├── DocViewer/               # Document viewing/management
│   ├── ChatExplorer/            # Chat search and exploration
│   ├── Agents/                  # AI agent implementations
│   └── [other components]/
├── lib/                         # Utility libraries
│   ├── llms/                    # LLM integrations
│   ├── tools/                   # AI tools and processors
│   ├── analytics/               # Analytics and logging
│   └── [other utilities]/
└── Utils/                       # Helper utilities
```

## 🔧 Getting Started

### Prerequisites
- Node.js 18+
- npm/yarn/pnpm
- Firebase project
- Pinecone account
- ElevenLabs API key
- OpenAI API key
- Google AI API key

### Environment Variables
Create a `.env.local` file with:

```bash
# Authentication
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Firebase
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# AI Services
OPENAI_API_KEY=your_openai_api_key
GOOGLE_AI_API_KEY=your_google_ai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GROQ_API_KEY=your_groq_api_key

# ElevenLabs
ELEVENLABS_API_KEY=your_elevenlabs_api_key
ELEVENLABS_AGENT_ID=your_agent_id
NEXT_PUBLIC_ELEVENLABS_API_KEY=your_public_elevenlabs_key
NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID=your_script_agent_id

# Pinecone
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_INDEX=your_index_name
```

### Installation & Setup

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🔄 Core Workflows

### 1. Script Upload & Processing
```
User Upload → Firebase Storage → Document Processing →
Vector Generation → Pinecone Storage → ElevenLabs Upload →
Knowledge Base Indexing → Agent Association
```

### 2. Chat Message Flow
```
User Input → Firestore Storage → Vector Search →
Context Retrieval → AI Processing → Audio Generation →
Response Storage → UI Update
```

### 3. Voice Rehearsal Flow
```
Voice Input → ElevenLabs Agent → Script Context →
AI Response → Voice Output → Session Management
```
