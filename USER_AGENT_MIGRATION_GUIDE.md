# User-Specific ElevenLabs Agent Migration Guide

## Overview

The CastMate application has been updated to use **user-specific ElevenLabs agents** instead of a single shared agent. This provides better privacy, personalization, and isolation between users.

## What Changed

### Before (Shared Agent System)
- All users shared a single ElevenLabs agent ID: `NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID="1WU4LPk9482VXQFb80aq"`
- No user isolation - all conversations and configurations were shared
- Privacy concerns with shared voice interactions

### After (User-Specific Agent System)
- Each user gets their own unique ElevenLabs agent
- Agent IDs follow the format: `{userEmail}-{UUIDv4}`
- Example: `<EMAIL>-a1b2c3d4-e5f6-7890-abcd-ef1234567890`
- Complete user isolation and personalization

## Implementation Details

### 1. Agent ID Generation
```typescript
// Format: userEmail-uuid
const agentId = `${userEmail}-${uuidv4()}`;
```

### 2. Automatic Agent Creation
- Agents are created on-demand when users access voice features
- **FIXED**: Removed agent creation from authentication flow to prevent race conditions
- **FIXED**: Added synchronization to prevent duplicate agent creation
- **FIXED**: Agent names now use the agent ID format instead of descriptive text

### 3. Database Schema Updates
User accounts now include:
```typescript
interface UserAccount {
  // ... existing fields
  elevenLabsAgentId?: string;
  agentCreatedAt?: string;
  agentStatus?: 'active' | 'failed' | 'creating';
  lastAgentCheck?: string;
  agentCreationAttempts?: number;
}
```

### 4. Duplicate Prevention
- **FIXED**: Added in-memory cache to prevent concurrent agent creation
- **FIXED**: Improved agent existence checking with better error handling
- **FIXED**: Agent creation only happens when voice features are actually used

## Files Modified

### Core Implementation
- `lib/userAgentManager.ts` - New agent management utilities
- `components/scriptreaderAI/elevenlabs.ts` - Added agent creation functions
- `app/api/auth/[...nextauth]/authOptions.ts` - Authentication callbacks
- `hooks/useUserAgent.ts` - React hook for agent management

### Component Updates
- `components/scriptreaderAI/Reader-modal.tsx` - Uses user-specific agents
- `components/VoiceAssistants/VoiceComponent.tsx` - Uses user-specific agents
- All ElevenLabs integration points updated

### API Endpoints
- `app/api/user-agent/route.ts` - Agent management API

## Environment Variables

### Deprecated (No Longer Used)
```bash
# This variable is no longer used - agents are created dynamically
NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID="1WU4LPk9482VXQFb80aq"
```

### Still Required
```bash
# ElevenLabs API key is still required for agent creation and management
NEXT_PUBLIC_ELEVENLABS_API_KEY="your_elevenlabs_api_key"
```

## Migration Steps

### For Existing Deployments

1. **Update Code**: Deploy the new user-specific agent system
2. **Environment Variables**: Remove `NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID` (optional)
3. **Clean Up Duplicates**: Remove duplicate agents created during initial implementation
4. **User Migration**: Existing users will automatically get new agents on next voice feature use
5. **Testing**: Verify that new users get unique agents

### For New Deployments

1. **Environment Setup**: Only `NEXT_PUBLIC_ELEVENLABS_API_KEY` is required
2. **Database**: Ensure Firestore is configured for user account storage
3. **Authentication**: NextAuth.js with Google OAuth must be configured

### Cleaning Up Duplicate Agents

If you have duplicate agents from the initial implementation:

1. **Identify Duplicates**:
   ```bash
   node scripts/cleanup-duplicate-agents.js
   ```

2. **Manual Cleanup**:
   - Go to [ElevenLabs Agents Dashboard](https://elevenlabs.io/app/conversational-ai/agents)
   - Delete agents with names like: `CastMate <NAME_EMAIL>`
   - Keep agents with names like: `<EMAIL>-uuid-string`

3. **Agent Name Patterns**:
   - ✅ **KEEP**: `<EMAIL>-a1b2c3d4-e5f6-7890-abcd-ef1234567890`
   - ❌ **DELETE**: `CastMate <NAME_EMAIL>`

## Error Handling

### Common Issues and Solutions

1. **Agent Creation Fails**
   - Check ElevenLabs API key validity
   - Verify API rate limits
   - Check network connectivity

2. **User Agent Not Found**
   - User will be prompted to sign out and back in
   - New agent will be created automatically

3. **Database Errors**
   - Ensure Firestore permissions are correct
   - Check user account document structure

## Testing

### Verify User-Specific Agents

1. **Sign in with different users**
2. **Check agent IDs in browser console**
3. **Verify each user gets unique agent**
4. **Test voice interactions are isolated**

### Console Logging
Look for these log messages:
```
[USER_AGENT_MANAGER] Generated agent ID: <EMAIL>-uuid
[ELEVENLABS] Creating new agent: <EMAIL>-uuid
[USER_AGENT_MANAGER] Agent created successfully: <EMAIL>-uuid
```

## Benefits

### Privacy
- Complete isolation between users
- No shared conversation history
- Individual voice preferences

### Personalization
- Each agent can be customized per user
- User-specific voice settings
- Personalized prompts and behavior

### Scalability
- No single point of failure
- Better resource distribution
- Easier user management

## Rollback Plan

If issues arise, you can temporarily revert by:

1. **Restore shared agent ID** in environment variables
2. **Update components** to use hardcoded agent ID
3. **Disable user agent creation** in auth callbacks

However, this is not recommended as it loses the benefits of user isolation.

## Support

For issues with the user-specific agent system:

1. Check browser console for error messages
2. Verify ElevenLabs API key and permissions
3. Ensure user authentication is working
4. Check Firestore database connectivity

## Future Enhancements

- Agent customization UI
- Voice preference management
- Agent performance analytics
- Bulk agent management tools
