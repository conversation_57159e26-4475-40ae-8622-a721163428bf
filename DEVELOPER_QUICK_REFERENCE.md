# CastMate Developer Quick Reference

## 🚀 Quick Start Commands

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Clean build artifacts
npm run clean
```

## 🔑 Essential Environment Variables

```bash
# Core Authentication
NEXTAUTH_SECRET=your_secret_here
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# AI Services (Required)
OPENAI_API_KEY=sk-...
GOOGLE_AI_API_KEY=your_google_ai_key
ELEVENLABS_API_KEY=your_elevenlabs_key
ELEVENLABS_AGENT_ID=your_agent_id

# Database & Storage
PINECONE_API_KEY=your_pinecone_key
PINECONE_INDEX=your_index_name
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
```

## 📁 Key File Locations

### Core Application Files
- `app/page.tsx` - Main landing page
- `app/layout.tsx` - Root layout with providers
- `app/providers.tsx` - Context providers setup

### Main Features
- `components/scriptreaderAI/Reader-modal.tsx` - Script reader interface
- `components/VoiceAssistants/VoiceComponent.tsx` - Voice chat
- `components/ChatArea.tsx` - Text chat interface
- `components/DocViewer/DocumentReader.tsx` - Document viewer

### API Endpoints
- `app/api/processScriptfile/route.ts` - Script processing
- `app/api/processMessage/route.ts` - Chat messages
- `app/api/transcribeAudio/route.ts` - Voice transcription
- `app/api/auth/[...nextauth]/route.ts` - Authentication

### Configuration
- `next.config.mjs` - Next.js configuration
- `components/firebase.ts` - Firebase configuration
- `lib/pinecone.tsx` - Pinecone setup

## 🔧 Common Development Tasks

### Adding a New API Endpoint
1. Create file in `app/api/your-endpoint/route.ts`
2. Export GET/POST functions
3. Add authentication check if needed
4. Handle errors appropriately

```typescript
import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.email) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  
  // Your logic here
  return NextResponse.json({ success: true });
}
```

### Adding a New React Component
1. Create component in appropriate directory
2. Use TypeScript interfaces for props
3. Follow naming conventions
4. Export from index if needed

```typescript
interface MyComponentProps {
  title: string;
  onAction: () => void;
}

export default function MyComponent({ title, onAction }: MyComponentProps) {
  return (
    <div className="p-4">
      <h2>{title}</h2>
      <button onClick={onAction}>Action</button>
    </div>
  );
}
```

### Integrating with Firebase
```typescript
import { db } from "components/firebase";
import { collection, addDoc, getDocs } from "firebase/firestore";

// Add document
await addDoc(collection(db, "users", userEmail, "collection"), data);

// Get documents
const snapshot = await getDocs(collection(db, "users", userEmail, "collection"));
const data = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
```

### Working with Pinecone
```typescript
import { Pinecone } from "@pinecone-database/pinecone";

const pinecone = new Pinecone();
const index = pinecone.Index(process.env.PINECONE_INDEX!);

// Query vectors
const results = await index.namespace(namespace).query({
  vector: embedding,
  topK: 5,
  includeMetadata: true
});
```

## 🎯 Feature Implementation Patterns

### Authentication Pattern
```typescript
// Client-side
import { useSession } from "next-auth/react";

const { data: session, status } = useSession();
if (status === "loading") return <Loading />;
if (!session) return <SignIn />;

// Server-side
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";

const session = await getServerSession(authOptions);
if (!session?.user?.email) {
  return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
}
```

### File Upload Pattern
```typescript
// 1. Upload to Firebase Storage
const storageRef = ref(storage, `files/${userId}/${fileName}`);
const uploadResult = await uploadBytes(storageRef, file);
const downloadURL = await getDownloadURL(uploadResult.ref);

// 2. Save metadata to Firestore
await setDoc(doc(db, "users", userId, "files", docId), {
  fileName,
  fileType: file.type,
  downloadURL,
  uploadedAt: serverTimestamp()
});

// 3. Process file via API
await fetch("/api/processScriptfile", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({ docId, userId, fileName, fileType, fileUrl: downloadURL })
});
```

### AI Integration Pattern
```typescript
// LLM Integration
import { createGroqClient } from "lib/llms/groq";

const groq = createGroqClient();
const response = await groq.chat.completions.create({
  messages: [{ role: "user", content: prompt }],
  model: "mixtral-8x7b-32768"
});

// Vector Search
import { getPineconeDocs } from "lib/pinecone";

const relevantDocs = await getPineconeDocs(
  process.env.PINECONE_INDEX!,
  namespace,
  query,
  { category: "script" }
);
```

## 🐛 Debugging Tips

### Common Issues & Solutions

1. **Build Errors**
   - Check TypeScript types
   - Verify import paths
   - Clear `.next` folder: `npm run clean`

2. **Authentication Issues**
   - Verify Google OAuth setup
   - Check NEXTAUTH_URL in production
   - Ensure NEXTAUTH_SECRET is set

3. **Firebase Connection**
   - Verify Firebase config
   - Check security rules
   - Ensure proper authentication

4. **API Errors**
   - Check environment variables
   - Verify API key permissions
   - Monitor rate limits

### Debug Tools
```typescript
// Add logging
console.log("[DEBUG]", { variable, context });

// Error boundaries
try {
  // risky operation
} catch (error) {
  console.error("[ERROR]", error);
  // handle gracefully
}

// Network debugging
// Check Network tab in browser dev tools
// Use Postman for API testing
```

## 📊 Performance Tips

### Optimization Strategies
1. **Use React.memo for expensive components**
2. **Implement proper loading states**
3. **Lazy load heavy components**
4. **Optimize images with Next.js Image component**
5. **Use proper caching strategies**

### Code Splitting
```typescript
import dynamic from 'next/dynamic';

const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <p>Loading...</p>,
  ssr: false
});
```

## 🔍 Testing Patterns

### Unit Testing
```typescript
import { render, screen } from '@testing-library/react';
import MyComponent from './MyComponent';

test('renders component correctly', () => {
  render(<MyComponent title="Test" onAction={() => {}} />);
  expect(screen.getByText('Test')).toBeInTheDocument();
});
```

### API Testing
```typescript
// Test API endpoints with Jest
import { POST } from './route';
import { NextRequest } from 'next/server';

test('API endpoint returns success', async () => {
  const request = new NextRequest('http://localhost/api/test', {
    method: 'POST',
    body: JSON.stringify({ test: 'data' })
  });
  
  const response = await POST(request);
  const data = await response.json();
  
  expect(data.success).toBe(true);
});
```

## 📚 Useful Resources

### Documentation Links
- [Next.js Docs](https://nextjs.org/docs)
- [Firebase Docs](https://firebase.google.com/docs)
- [Pinecone Docs](https://docs.pinecone.io/)
- [ElevenLabs API](https://elevenlabs.io/docs)
- [OpenAI API](https://platform.openai.com/docs)

### Internal Documentation
- `APPLICATION_INDEX.md` - Complete application overview
- `SCRIPT_READER_ANALYSIS.md` - Technical deep dive
- `GOOGLE_AI_SETUP.md` - AI service setup
- `ELEVENLABS_RAG_INDEXING_IMPLEMENTATION.md` - Voice integration

---

*Keep this reference handy for quick development tasks and troubleshooting!*
